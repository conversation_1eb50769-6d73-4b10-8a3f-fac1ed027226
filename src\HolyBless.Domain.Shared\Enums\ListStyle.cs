﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HolyBless.Enums
{
    public enum ListStyle
    {
        ImageCard = 0,     //A card with image and summary
        SummaryCard = 1,   //A card with summary only
        /*VideoCard = 2,     //A card with video player icon
        AudioCard = 3,     //A card with audio player icon
        EBookCard = 4,     //A card with book cover image + colllection name
        CDCard = 5,        //A card with CD icon
        FolderCard = 6,    //A card with folder icon
        TableList = 7,     //A table list
        */
        ArticleTree = 8,   //Leftside: A tree list for articles
        CollectionTree = 9,//Leftside: A tree list for collections
        CollectionArticleTree = 10, //Lefside: A tree list for collections and articles
    }
}