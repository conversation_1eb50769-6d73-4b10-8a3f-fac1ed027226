using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Collections.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Collections;
using HolyBless.Interfaces;
using HolyBless.Results;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Collections
{
    public class ReadOnlyCollectionAppService : ApplicationService, IReadOnlyCollectionAppService
    {
        protected readonly ICollectionRepository _repository;
        protected readonly AppConfig _settings;
        protected readonly IRepository<CollectionToArticle> _collectionToArticleRepository;
        protected readonly IRepository<CollectionToFile> _collectionToFileRepository;

        public ReadOnlyCollectionAppService(
            ICollectionRepository repository,
            AppConfig settings,
            IRepository<CollectionToArticle> collectionToArticleRepository,
            IRepository<CollectionToFile> collectionToFileRepository)
        {
            _repository = repository;
            _settings = settings;
            _collectionToArticleRepository = collectionToArticleRepository;
            _collectionToFileRepository = collectionToFileRepository;
        }

        public async Task<CollectionDto> GetAsync(int id)
        {
            var collection = await _repository
                .FirstOrDefaultAsync(x => x.Id == id);

            Check.NotNull(collection, nameof(collection));
            var collectionDto = ObjectMapper.Map<Collection, CollectionDto>(collection);

            return collectionDto;
        }
        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionDto>> GetListAsync(CollectionSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();

            var query = queryable
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status.Value)
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId.Value)
                .Include(x => x.ContentCode)
                .OrderBy(input.Sorting ?? "CollectionName");

            var collections = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            return new PagedResultDto<CollectionDto>(
                totalCount,
                ObjectMapper.Map<List<Collection>, List<CollectionDto>>(collections)
            );
        }
        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionToArticleDto>> GetCollectionArticlesAsync(CollectionArticleSearchDto input)
        {
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .WhereIf(input.Status.HasValue, x => x.Article.Status == input.Status.Value)
                .OrderBy(input.Sorting ?? nameof(CollectionToArticle.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToArticle>, List<CollectionToArticleDto>>(items);
            return new PagedResultDto<CollectionToArticleDto>(totalCount, dtos);
        }
        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionToFileDto>> GetCollectionFilesAsync(CollectionFileSearchDto input)
        {
            var queryable = await _collectionToFileRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .OrderBy(input.Sorting ?? nameof(CollectionToFile.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToFile>, List<CollectionToFileDto>>(items);
            return new PagedResultDto<CollectionToFileDto>(totalCount, dtos);
        }

        //Return data for list view for ImageCard and SummaryCard style
        public async Task<CollectionSummaryResult> GetCollectionSummaryAsync(int collectionId, CollectionSummaryRequest request)
        {
            var result = await _repository.GetCollectionSummaryAsync(collectionId, request);
            return result;
        }

        //Return Article titles for given collection for list sytle: ArticleTree
        public async Task<List<ArticleTitleDto>> GetCollectionArticleTitlesAsync(int collectionId)
        {
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == collectionId)
                .OrderByDescending(x => x.Article.DeliveryDate)
                .Select(x => new ArticleTitleDto
                {
                    Id = x.Article.Id,
                    Title = x.Article.Title,
                });
            return await AsyncExecuter.ToListAsync(query);
        }

        public async Task<CollectionDto?> GetFirstByChannelIdAsync(int channelId)
        {
            var collection = await _repository
                .FirstOrDefaultAsync(x => x.ChannelId == channelId);
            if (collection == null)
            {
                return null;
            }

            var collectionDto = ObjectMapper.Map<Collection, CollectionDto>(collection);
            return collectionDto;
        }

        public async Task<CollectionDto?> GetLanguageMatchingCollectionAsync(string contentCode, string languageCode)
        {
            var queryable = await _repository.GetQueryableAsync();

            var collection = await queryable
                .Where(x => (x.LanguageCode == null || x.LanguageCode == languageCode) && x.ContentCode == contentCode)
                .FirstOrDefaultAsync();
            if (collection == null)
            {
                return null;
            }
            return ObjectMapper.Map<Collection, CollectionDto>(collection);
        }

        public async Task<List<CollectionTreeDto>> GetCollectionTreeAsync(int collectionId)
        {
            // Use the optimized repository method that uses CTE for single DB call
            var allCollections = await _repository.GetAllDescendantsAsync(collectionId);

            // Convert all collections to CollectionTreeDto
            var collectionDtos = ObjectMapper.Map<List<Collection>, List<CollectionTreeDto>>(allCollections);

            // Create a dictionary for quick lookup
            var collectionDict = collectionDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootCollections = new List<CollectionTreeDto>();

            foreach (var collection in collectionDtos)
            {
                if (collection.ParentCollectionId.HasValue && collectionDict.TryGetValue(collection.ParentCollectionId.Value, out var parentDto))
                {
                    // Add as child to parent
                    parentDto.Children.Add(collection);
                }
                else if (collection.ParentCollectionId == collectionId)
                {
                    // Add as root collection (direct child of the specified collection)
                    collection.IsRoot = true;
                    rootCollections.Add(collection);
                }
            }

            return rootCollections;
        }

        public async Task<List<CollectionArticleTreeDto>> GetCollectionTreeAndArticleTitlesAsync(int collectionId)
        {
            // Get the collection tree structure
            var allCollections = await _repository.GetAllDescendantsAsync(collectionId);

            // Convert all collections to CollectionArticleTreeDto
            var collectionDtos = ObjectMapper.Map<List<Collection>, List<CollectionArticleTreeDto>>(allCollections);

            // Create a dictionary for quick lookup
            var collectionDict = collectionDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootCollections = new List<CollectionArticleTreeDto>();

            foreach (var collection in collectionDtos)
            {
                if (collection.ParentCollectionId.HasValue && collectionDict.TryGetValue(collection.ParentCollectionId.Value, out var parentDto))
                {
                    // Add as child to parent
                    parentDto.Children.Add(collection);
                }
                else if (collection.ParentCollectionId == collectionId)
                {
                    // Add as root collection (direct child of the specified collection)
                    collection.IsRoot = true;
                    rootCollections.Add(collection);
                }
            }

            // Now populate articles for leaf collections (collections with no children)
            var leafCollections = collectionDtos.Where(c => c.Children.Count == 0).ToList();

            if (leafCollections.Any())
            {
                // Get all articles for leaf collections
                var collectionToArticleQueryable = await _collectionToArticleRepository.GetQueryableAsync();
                var articlesWithCollectionId = await AsyncExecuter.ToListAsync(
                    collectionToArticleQueryable
                        .Where(x => leafCollections.Select(lc => lc.Id).Contains(x.CollectionId))
                        .OrderByDescending(x => x.Article.DeliveryDate)
                        .Select(x => new
                        {
                            CollectionId = x.CollectionId,
                            Article = new ArticleTitleDto
                            {
                                Id = x.Article.Id,
                                Title = x.Article.Title,
                            }
                        })
                );

                // Group articles by collection and assign to respective collections
                var articlesByCollection = articlesWithCollectionId
                    .GroupBy(x => x.CollectionId)
                    .ToDictionary(g => g.Key, g => g.Select(x => x.Article).ToList());

                foreach (var leafCollection in leafCollections)
                {
                    if (articlesByCollection.TryGetValue(leafCollection.Id, out var articles))
                    {
                        leafCollection.Articles = articles;
                    }
                }
            }

            return rootCollections;
        }
    }
}