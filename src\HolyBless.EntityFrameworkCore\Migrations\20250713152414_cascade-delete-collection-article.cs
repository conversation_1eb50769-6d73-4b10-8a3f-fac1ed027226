﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class cascadedeletecollectionarticle : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToArticles_Articles_ArticleId",
                table: "CollectionToArticles");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToArticles_Articles_ArticleId",
                table: "CollectionToArticles",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToArticles_Articles_ArticleId",
                table: "CollectionToArticles");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToArticles_Articles_ArticleId",
                table: "CollectionToArticles",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id");
        }
    }
}
