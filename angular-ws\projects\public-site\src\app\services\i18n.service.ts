import { Injectable, signal, computed, inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

export interface Language {
  code: string;
  label: string;
  flag: string;
}

export interface TranslationResource {
  [key: string]: string | TranslationResource;
}

@Injectable({
  providedIn: 'root',
})
export class I18nService {
  private document = inject(DOCUMENT);

  private supportedLanguages: Language[] = [
    { code: 'zh-CN', label: '简体中文', flag: 'cn' },
    { code: 'zh-TW', label: '繁體中文', flag: 'tw' },
    { code: 'en-US', label: 'English', flag: 'us' },
  ];

  currentLanguage = signal<string>(this.getStoredLanguage() || 'zh-CN');

  // 翻译资源
  private translations = signal<Record<string, TranslationResource>>({});

  currentLanguageInfo = computed(
    () =>
      this.supportedLanguages.find(
        (lang) => lang.code === this.currentLanguage(),
      ) || this.supportedLanguages[0],
  );

  constructor() {
    this.loadTranslations();
  }

  private getStoredLanguage(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('selectedLanguage');
    }
    return null;
  }

  // 存储语言设置
  private storeLanguage(languageCode: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('selectedLanguage', languageCode);
    }
  }

  // 加载翻译资源
  private async loadTranslations() {
    try {
      const [zhCN, zhTW, enUS] = await Promise.all([
        import('../../assets/i18n/zh-CN.json'),
        import('../../assets/i18n/zh-TW.json'),
        import('../../assets/i18n/en-US.json'),
      ]);

      this.translations.set({
        'zh-CN': zhCN.default,
        'zh-TW': zhTW.default,
        'en-US': enUS.default,
      });
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  }

  // 切换语言
  setLanguage(languageCode: string) {
    if (this.supportedLanguages.some((lang) => lang.code === languageCode)) {
      this.currentLanguage.set(languageCode);
      this.storeLanguage(languageCode);
      this.updateDocumentLanguage(languageCode);
    }
  }

  // 更新文档语言属性
  private updateDocumentLanguage(languageCode: string) {
    const lang = languageCode.split('-')[0];
    this.document.documentElement.lang = lang;
  }

  // 获取翻译文本
  translate(key: string, params?: Record<string, any>): string {
    const currentTranslations = this.translations()[this.currentLanguage()];
    if (!currentTranslations) {
      return key;
    }

    const translation = this.getNestedValue(currentTranslations, key);
    if (typeof translation !== 'string') {
      return key;
    }

    // 简单的参数替换
    if (params) {
      return Object.keys(params).reduce((text, param) => {
        return text.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
      }, translation);
    }

    return translation;
  }

  // 获取嵌套对象的值
  private getNestedValue(
    obj: TranslationResource,
    key: string,
  ): string | TranslationResource {
    return (
      key.split('.').reduce((current: any, k) => {
        return current && typeof current === 'object' ? current[k] : undefined;
      }, obj) || key
    );
  }

  // 获取支持的语言列表
  getSupportedLanguages(): Language[] {
    return this.supportedLanguages;
  }
}
