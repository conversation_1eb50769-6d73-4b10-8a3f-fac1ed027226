using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Collections.Dtos;
using HolyBless.Enums;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Collections
{
    public abstract class CollectionAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
            where TStartupModule : IAbpModule
    {
        private readonly ICollectionAppService _collectionAppService;

        protected CollectionAppService_Tests()
        {
            _collectionAppService = GetRequiredService<ICollectionAppService>();
        }

        [Fact]
        public async Task Should_Get_List_Of_Collections()
        {
            // Act
            var result = await _collectionAppService.GetListAsync(new CollectionSearchDto());

            // Assert
            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(c => c.Name == "Collection1");

            var item = result.Items.FirstOrDefault(c => c.Name == "Collection1");
            item.ShouldNotBeNull();
            item.ContentCode.ShouldBe("Code1");

            //Action
            var singleItem = await _collectionAppService.GetAsync(item.Id);

            singleItem.ShouldNotBeNull();
            singleItem.ContentCode.ShouldBe("Code1");
        }

        [Fact]
        public async Task Should_Create_A_New_Collection()
        {
            // Act
            var result = await _collectionAppService.CreateAsync(
                new CreateUpdateCollectionDto
                {
                    Name = "NewCollection",
                    ContentCode = "Code1",
                    LanguageCode = "EN",
                    Description = "New Collection Description",
                    Status = PublishStatus.Published
                }
            );

            // Assert
            result.Id.ShouldNotBe(0);
            result.Name.ShouldBe("NewCollection");
        }

        [Fact]
        public async Task Should_Not_Create_A_Collection_Without_CollectionName()
        {
            // Act & Assert
            await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _collectionAppService.CreateAsync(
                    new CreateUpdateCollectionDto
                    {
                        ContentCode = "Code1",
                        LanguageCode = "EN",
                        Description = "Collection without name",
                        Status = PublishStatus.Published
                    }
                );
            });
        }

        [Fact]
        public async Task Should_Update_Existing_Collection()
        {
            // Arrange
            var collection = await _collectionAppService.CreateAsync(
                new CreateUpdateCollectionDto
                {
                    Name = "UpdateCollection",
                    ContentCode = "Code1",
                    LanguageCode = "EN",
                    Description = "Collection to be updated",
                    Status = PublishStatus.Published
                }
            );

            // Act
            var result = await _collectionAppService.UpdateAsync(
                collection.Id,
                new CreateUpdateCollectionDto
                {
                    Name = "UpdatedCollection",
                    ContentCode = "Code1",
                    LanguageCode = "EN",
                    Description = "Updated Collection Description",
                    Status = PublishStatus.Published
                }
            );

            // Assert
            result.Name.ShouldBe("UpdatedCollection");
        }

        [Fact]
        public async Task Should_Delete_Existing_Collection()
        {
            // Arrange
            var collection = await _collectionAppService.CreateAsync(
                new CreateUpdateCollectionDto
                {
                    Name = "DeleteCollection",
                    ContentCode = "1",
                    LanguageCode = "EN",
                    Description = "Collection to be deleted",
                    Status = PublishStatus.Published
                }
            );

            // Act
            await _collectionAppService.DeleteAsync(collection.Id);

            // Assert
            var result = await _collectionAppService.GetListAsync(new CollectionSearchDto());
            result.Items.ShouldNotContain(c => c.Name == "DeleteCollection");
        }

        [Fact]
        public async Task Should_Get_Collection_Tree_With_Article_Titles()
        {
            // Arrange - Get an existing collection from test data
            var collectionsResult = await _collectionAppService.GetListAsync(new CollectionSearchDto());
            collectionsResult.TotalCount.ShouldBeGreaterThan(0);
            
            var testCollection = collectionsResult.Items.First();
            var readOnlyCollectionAppService = GetRequiredService<IReadOnlyCollectionAppService>();

            // Act
            var result = await readOnlyCollectionAppService.GetCollectionTreeAndArticleTitlesAsync(testCollection.Id);

            // Assert
            result.ShouldNotBeNull();
            // The result should be a list (may be empty if the collection has no children)
            result.ShouldBeOfType<List<CollectionArticleTreeDto>>();
            
            // If there are any leaf collections, they may have article titles
            var leafCollections = GetLeafCollections(result);
            foreach (var leafCollection in leafCollections)
            {
                leafCollection.Articles.ShouldNotBeNull();
                // Articles may be empty, which is valid
            }
        }

        private List<CollectionArticleTreeDto> GetLeafCollections(List<CollectionArticleTreeDto> collections)
        {
            var leafCollections = new List<CollectionArticleTreeDto>();
            foreach (var collection in collections)
            {
                if (collection.Children.Count == 0)
                {
                    leafCollections.Add(collection);
                }
                else
                {
                    leafCollections.AddRange(GetLeafCollections(collection.Children));
                }
            }
            return leafCollections;
        }
    }
}