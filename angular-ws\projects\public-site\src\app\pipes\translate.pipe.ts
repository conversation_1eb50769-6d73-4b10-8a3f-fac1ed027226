import { Pipe, PipeTransform, inject, OnD<PERSON>roy } from '@angular/core';
import { I18nService } from '../services/i18n.service';
import { effect, Injector, runInInjectionContext } from '@angular/core';

@Pipe({
  name: 'translate',
  pure: false,
  standalone: true,
})
export class TranslatePipe implements PipeTransform, OnDestroy {
  private i18nService = inject(I18nService);
  private injector = inject(Injector);
  private lastKey = '';
  private lastValue = '';

  constructor() {
    runInInjectionContext(this.injector, () => {
      effect(() => {
        this.i18nService.currentLanguage();
        this.lastKey = '';
      });
    });
  }

  transform(key: string, params?: Record<string, any>): string {
    if (this.lastKey === key && !params) {
      return this.lastValue;
    }

    this.lastKey = key;
    this.lastValue = this.i18nService.translate(key, params);
    return this.lastValue;
  }

  ngOnDestroy() {}
}
