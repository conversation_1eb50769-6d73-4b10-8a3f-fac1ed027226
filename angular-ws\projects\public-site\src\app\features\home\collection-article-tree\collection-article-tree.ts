import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-collection-artical-tree',
  standalone: true,
  imports: [CommonModule, TreeModule],
  templateUrl: './collection-article-tree.html',
  styleUrls: ['./collection-article-tree.scss'],
})
export class CollectionArticleTreeComponent {
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #route = inject(ActivatedRoute);
  collectionId: number | null = null;
  constructor() {}
  files: TreeNode[] = [
    {
      label: 'Root',
      data: 'Root Node',
      expandedIcon: 'pi pi-folder-open',
      collapsedIcon: 'pi pi-folder',
    },
  ];

  selectedFile!: TreeNode;
  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.collectionId = params['collectionId'];
      this.loadCollectionSummary();
    });
  }

  loadCollectionSummary() {
    this.#ReadOnlyCollectionService
      .getCollectionTreeAndArticleTitles(this.collectionId!)
      .subscribe({
        next: (data) => {
          console.log('data', data);
          this.files = data.map((node) => ({
            label: node.name,
            data: node.name,
          }));
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
        },
      });
  }
}
