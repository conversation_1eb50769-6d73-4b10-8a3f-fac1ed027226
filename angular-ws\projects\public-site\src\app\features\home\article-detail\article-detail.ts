import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';

@Component({
  selector: 'app-artical-detail',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './article-detail.html',
  styleUrls: ['./article-detail.scss'],
})
export class ArticleDetailComponent {
  #route = inject(ActivatedRoute);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  articleId: number | null = null;
  articleDetail: ArticleAggregateResult | null = null;

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.articleId = params['articleId'];
      this.loadArticleDetail();
    });
  }

  loadArticleDetail() {
    if (!this.articleId) return;
    this.#ReadOnlyArticleService.getArticleAggregate(this.articleId).subscribe({
      next: (data) => {
        this.articleDetail = data;
      },
      error: (error) => {
        console.error('获取文章详情失败:', error);
      },
    });
  }
}
