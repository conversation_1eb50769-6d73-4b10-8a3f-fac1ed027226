using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Entities.Buckets;
using Microsoft.EntityFrameworkCore;
using HolyBless.Buckets.Dtos;
using System.Linq;
using System.Linq.Dynamic.Core;
using Volo.Abp;

namespace HolyBless.Buckets
{
    [RemoteService(false)]
    public class ReadOnlyBucketFileAppService : ApplicationService, IReadOnlyBucketFileAppService
    {
        private readonly IRepository<BucketFile, int> _repository;

        public ReadOnlyBucketFileAppService(
            IRepository<BucketFile, int> repository
            )
        {
            _repository = repository;
        }

        public async Task<BucketFileDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var bucketFile = await queryable.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(bucketFile, nameof(BucketFile));
            return ObjectMapper.Map<BucketFile, BucketFileDto>(bucketFile);
        }

        public async Task<PagedResultDto<BucketFileDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .OrderBy(input.Sorting ?? "FileName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var bucketFiles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<BucketFileDto>(
                totalCount,
                ObjectMapper.Map<List<BucketFile>, List<BucketFileDto>>(bucketFiles)
            );
        }
    }
}