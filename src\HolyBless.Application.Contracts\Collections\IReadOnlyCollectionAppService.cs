using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Collections.Dtos;
using HolyBless.Results;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Collections
{
    public interface IReadOnlyCollectionAppService
    {
        Task<CollectionDto> GetAsync(int id);

        Task<PagedResultDto<CollectionDto>> GetListAsync(CollectionSearchDto input);

        Task<PagedResultDto<CollectionToArticleDto>> GetCollectionArticlesAsync(CollectionArticleSearchDto input);

        Task<PagedResultDto<CollectionToFileDto>> GetCollectionFilesAsync(CollectionFileSearchDto input);

        Task<CollectionSummaryResult> GetCollectionSummaryAsync(int collectionId, CollectionSummaryRequest request);

        Task<List<ArticleTitleDto>> GetCollectionArticleTitlesAsync(int collectionId);

        Task<CollectionDto?> GetFirstByChannelIdAsync(int channelId);

        Task<CollectionDto?> GetLanguageMatchingCollectionAsync(string contentCode, string languageCode);

        Task<List<CollectionTreeDto>> GetCollectionTreeAsync(int collectionId);

        Task<List<CollectionArticleTreeDto>> GetCollectionTreeAndArticleTitlesAsync(int collectionId);
    }
}