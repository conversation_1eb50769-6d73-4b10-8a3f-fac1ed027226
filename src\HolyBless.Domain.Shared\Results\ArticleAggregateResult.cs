using System;
using System.Collections.Generic;
using HolyBless.Enums;

namespace HolyBless.Results
{
    public class ArticleAggregateResult
    {
        // Article properties
        public int Id { get; set; }

        public string Title { get; set; } = default!;
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public DateTime DeliveryDate { get; set; }
        public ArticleContentCategory ArticleContentCategory { get; set; }
        public PublishStatus Status { get; set; }
        public string? Content { get; set; }
        public string? Memo { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }

        public string? ThumbnailUrl { get; set; }

        // Article files
        public List<ArticleFileAggregateResult> ArticleFiles { get; set; } = [];
    }
}