using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Albums
{
    public interface IAlbumAppService : IReadOnlyAlbumAppService, ICrudAppService<
            AlbumDto, // Used to show albums
            int, // Primary key of the album entity
            AlbumSearchDto, // Used for paging/sorting
            CreateUpdateAlbumDto> // Used to create/update an album
    {
        Task<List<AlbumToFileDto>> AddFilesToAlbumAsync(int albumId, List<CreateUpdateAlbumToFileDto> files);

        Task RemoveFileFromAlbumAsync(int albumId, int fileId);

        Task<AlbumToFileDto> UpdateAlbumFileAsync(int albumId, int fileId, CreateUpdateAlbumToFileDto input);

        Task ReorderAlbumFilesAsync(int albumId, List<int> fileIds);
    }
}
