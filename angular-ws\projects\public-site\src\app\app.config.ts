import {
  ApplicationConfig,
  provideZoneChangeDetection,
  isDevMode,
  inject,
  provideAppInitializer,
  importProvidersFrom,
} from '@angular/core';
import { provideServiceWorker } from '@angular/service-worker';
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {
  provideRouter,
  Router,
  withEnabledBlockingInitialNavigation,
  withInMemoryScrolling,
} from '@angular/router';
import Aura from '@primeng/themes/aura';
import { providePrimeNG } from 'primeng/config';
import { HttpHandler } from '@angular/common/http';
import { appRoutes } from '@/app.routes';
import { CustomAuraPreset } from './theme.config';
import { CoreModule } from '@abp/ng.core';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import en from '@angular/common/locales/en';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      appRoutes,
      withInMemoryScrolling({
        anchorScrolling: 'enabled',
        scrollPositionRestoration: 'enabled',
      }),
      withEnabledBlockingInitialNavigation(),
    ),
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: CustomAuraPreset,
        options: {
          darkModeSelector: '.app-dark',
        },
      },
    }),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    // 替代APP_INITIALIZER的初始化
    // DynamicRouteService,
    provideAppInitializer(() => {
      // const dynamicRouteService = inject(DynamicRouteService);
      // return dynamicRouteService.loadDynamicRoutes()
    }),
    importProvidersFrom(
      CoreModule.forRoot({
        environment: {
          apis: {
            default: {
              url: 'https://dev.xanshuo.cn',
            },
          },
        },
        registerLocaleFn: () =>
          new Promise((resolve) => {
            registerLocaleData(zh, 'zh-CN');
            registerLocaleData(en, 'en-US');
            resolve(null);
          }),
      }),
    ),
  ],
};
