import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { ArticleAggregateResult } from '../results/models';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyArticleService {
  apiName = 'Default';
  

  getArticleAggregate = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult>({
      method: 'GET',
      url: `/api/app/read-only-article/article-aggregate/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByCollectionId = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: `/api/app/read-only-article/article-aggregates-by-collection-id/${collectionId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
