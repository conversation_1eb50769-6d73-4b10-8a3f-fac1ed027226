import type { ChannelTreeDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyChannelService {
  apiName = 'Default';
  

  getChannelTree = (languageCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-channel/channel-tree',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
