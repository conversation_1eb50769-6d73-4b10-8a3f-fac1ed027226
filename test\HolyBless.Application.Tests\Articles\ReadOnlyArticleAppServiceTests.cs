using System;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Articles;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Results;
using Shouldly;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace HolyBless.Articles
{
    public abstract class ReadOnlyArticleAppServiceTests<TStartupModule> : HolyBlessTestBase<TStartupModule>
        where TStartupModule : IAbpModule
    {
        private readonly IReadOnlyArticleAppService _readOnlyArticleAppService;

        protected ReadOnlyArticleAppServiceTests()
        {
            _readOnlyArticleAppService = GetRequiredService<IReadOnlyArticleAppService>();
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Return_Article_With_Files_And_Thumbnail()
        {
            // Arrange - Create test data
            var article = new Article
            {
                Title = "Test Article",
                Description = "Test Description",
                Keywords = "test, keywords",
                Views = 100,
                Likes = 50,
                DeliveryDate = DateTime.Now,
                ArticleContentCategory = ArticleContentCategory.Article,
                Status = PublishStatus.Published,
                Content = "Test content",
                Memo = "Test memo"
            };

            var thumbnailFile = new BucketFile
            {
                FileName = "thumbnail.jpg",
                Title = "Thumbnail Image",
                RelativePathInBucket = "images/thumbnails",
                MediaType = MediaType.Image,
                ContentCategory = ContentCategory.Thumbnail,
                DeliveryDate = DateTime.Now
            };

            var articleFile = new ArticleFile
            {
                Title = "Primary File",
                Description = "Primary article file",
                IsPrimary = true
            };

            var bucketFile = new BucketFile
            {
                FileName = "document.pdf",
                Title = "Article Document",
                RelativePathInBucket = "documents",
                MediaType = MediaType.Document,
                ContentCategory = ContentCategory.Document,
                DeliveryDate = DateTime.Now
            };

            // Insert test data
            await WithUnitOfWorkAsync(async () =>
            {
                var articleRepository = GetRequiredService<IRepository<Article, int>>();
                var bucketFileRepository = GetRequiredService<IRepository<BucketFile, int>>();
                var articleFileRepository = GetRequiredService<IRepository<ArticleFile, int>>();

                thumbnailFile = await bucketFileRepository.InsertAsync(thumbnailFile, autoSave: true);
                bucketFile = await bucketFileRepository.InsertAsync(bucketFile, autoSave: true);
                
                article.ThumbnailFileId = thumbnailFile.Id;
                article = await articleRepository.InsertAsync(article, autoSave: true);
                
                articleFile.ArticleId = article.Id;
                articleFile.FileId = bucketFile.Id;
                await articleFileRepository.InsertAsync(articleFile, autoSave: true);
            });

            // Act
            var result = await _readOnlyArticleAppService.GetArticleAggregateAsync(article.Id);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(article.Id);
            result.Title.ShouldBe("Test Article");
            result.Description.ShouldBe("Test Description");
            result.Keywords.ShouldBe("test, keywords");
            result.Views.ShouldBe(100);
            result.Likes.ShouldBe(50);
            result.ArticleContentCategory.ShouldBe(ArticleContentCategory.Article);
            result.Status.ShouldBe(PublishStatus.Published);
            result.Content.ShouldBe("Test content");
            result.Memo.ShouldBe("Test memo");

            // Thumbnail assertions
            result.ThumbnailUrl.ShouldNotBeNull();

            // Article files assertions
            result.ArticleFiles.ShouldNotBeEmpty();
            result.ArticleFiles.Count.ShouldBe(1);
            
            var fileResult = result.ArticleFiles[0];
            fileResult.ArticleId.ShouldBe(article.Id);
            fileResult.FileId.ShouldBe(bucketFile.Id);
            fileResult.Title.ShouldBe("Primary File");
            fileResult.Description.ShouldBe("Primary article file");
            fileResult.IsPrimary.ShouldBeTrue();
            fileResult.FileName.ShouldBe("document.pdf");
            fileResult.MediaType.ShouldBe(MediaType.Document);
            fileResult.ContentCategory.ShouldBe(ContentCategory.Document);
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Throw_EntityNotFoundException_For_NonExistent_Article()
        {
            // Act & Assert
            await Should.ThrowAsync<EntityNotFoundException>(async () =>
            {
                await _readOnlyArticleAppService.GetArticleAggregateAsync(999999);
            });
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Handle_Article_Without_Thumbnail()
        {
            // Arrange
            var article = new Article
            {
                Title = "Article Without Thumbnail",
                Description = "Test Description",
                DeliveryDate = DateTime.Now,
                ArticleContentCategory = ArticleContentCategory.Article,
                Status = PublishStatus.Published,
                ThumbnailFileId = null // No thumbnail
            };

            await WithUnitOfWorkAsync(async () =>
            {
                var articleRepository = GetRequiredService<IRepository<Article, int>>();
                article = await articleRepository.InsertAsync(article, autoSave: true);
            });

            // Act
            var result = await _readOnlyArticleAppService.GetArticleAggregateAsync(article.Id);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(article.Id);
            result.Title.ShouldBe("Article Without Thumbnail");
            result.ThumbnailUrl.ShouldBeNull();
            result.ArticleFiles.ShouldBeEmpty();
        }

        [Fact]
        public async Task GetArticleAggregatesByCollectionIdAsync_Should_Return_Articles_For_Collection()
        {
            // Arrange
            int collectionId = 0;
            int articleId1 = 0;
            int articleId2 = 0;

            await WithUnitOfWorkAsync(async () =>
            {
                var collectionRepository = GetRequiredService<IRepository<Entities.Collections.Collection, int>>();
                var articleRepository = GetRequiredService<IRepository<Article, int>>();
                var collectionToArticleRepository = GetRequiredService<IRepository<Entities.Collections.CollectionToArticle>>();

                // Create a test collection
                var collection = new Entities.Collections.Collection
                {
                    Name = "Test Collection",
                    Description = "Collection for testing",
                    Status = PublishStatus.Published
                };
                collection = await collectionRepository.InsertAsync(collection, autoSave: true);
                collectionId = collection.Id;

                // Create test articles
                var article1 = new Article
                {
                    Title = "Test Article 1",
                    Description = "First test article",
                    DeliveryDate = DateTime.Now,
                    ArticleContentCategory = ArticleContentCategory.Article,
                    Status = PublishStatus.Published
                };
                article1 = await articleRepository.InsertAsync(article1, autoSave: true);
                articleId1 = article1.Id;

                var article2 = new Article
                {
                    Title = "Test Article 2",
                    Description = "Second test article",
                    DeliveryDate = DateTime.Now,
                    ArticleContentCategory = ArticleContentCategory.Article,
                    Status = PublishStatus.Published
                };
                article2 = await articleRepository.InsertAsync(article2, autoSave: true);
                articleId2 = article2.Id;

                // Link articles to collection
                var collectionToArticle1 = new Entities.Collections.CollectionToArticle
                {
                    CollectionId = collectionId,
                    ArticleId = articleId1,
                    Weight = 1
                };
                await collectionToArticleRepository.InsertAsync(collectionToArticle1, autoSave: true);

                var collectionToArticle2 = new Entities.Collections.CollectionToArticle
                {
                    CollectionId = collectionId,
                    ArticleId = articleId2,
                    Weight = 2
                };
                await collectionToArticleRepository.InsertAsync(collectionToArticle2, autoSave: true);
            });

            // Act
            var results = await _readOnlyArticleAppService.GetArticleAggregatesByCollectionIdAsync(collectionId);

            // Assert
            results.ShouldNotBeNull();
            results.Count.ShouldBe(2);
            
            var articleIds = results.Select(r => r.Id).ToList();
            articleIds.ShouldContain(articleId1);
            articleIds.ShouldContain(articleId2);

            var article1Result = results.First(r => r.Id == articleId1);
            article1Result.Title.ShouldBe("Test Article 1");
            article1Result.Description.ShouldBe("First test article");

            var article2Result = results.First(r => r.Id == articleId2);
            article2Result.Title.ShouldBe("Test Article 2");
            article2Result.Description.ShouldBe("Second test article");
        }

        [Fact]
        public async Task GetArticleAggregatesByCollectionIdAsync_Should_Return_Empty_List_For_Collection_Without_Articles()
        {
            // Arrange
            int collectionId = 0;

            await WithUnitOfWorkAsync(async () =>
            {
                var collectionRepository = GetRequiredService<IRepository<Entities.Collections.Collection, int>>();

                // Create a test collection without articles
                var collection = new Entities.Collections.Collection
                {
                    Name = "Empty Collection",
                    Description = "Collection without articles",
                    Status = PublishStatus.Published
                };
                collection = await collectionRepository.InsertAsync(collection, autoSave: true);
                collectionId = collection.Id;
            });

            // Act
            var results = await _readOnlyArticleAppService.GetArticleAggregatesByCollectionIdAsync(collectionId);

            // Assert
            results.ShouldNotBeNull();
            results.Count.ShouldBe(0);
        }
    }

    // Concrete test class for Application Tests
    public class ApplicationReadOnlyArticleAppServiceTests : ReadOnlyArticleAppServiceTests<HolyBlessApplicationTestModule>
    {
    }
}
