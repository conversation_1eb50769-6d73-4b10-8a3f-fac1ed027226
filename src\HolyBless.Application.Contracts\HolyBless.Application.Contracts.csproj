<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>HolyBless</RootNamespace>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\HolyBless.Domain.Shared\HolyBless.Domain.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.1.1" />
  </ItemGroup>
</Project>