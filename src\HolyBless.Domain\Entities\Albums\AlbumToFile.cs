﻿using HolyBless.Entities.Buckets;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Entities;

namespace HolyBless.Entities.Albums
{
    public class AlbumToFile: Entity, ISoftDelete
    {
        public int AlbumId { get; set; }
        public Album Album { get; set; } = default!;
        public int FileId { get; set; }
        public string? Title { get; set; } = default!; //Title of the file in the album
        public BucketFile BucketFile { get; set; } = default!;
        public int Weight { get; set; } = 0;
        public bool IsDeleted { get; set; }

        public override object?[] GetKeys()
        {
            return [AlbumId, FileId];
        }

        public AlbumToFile()
        {
            
        }
        public AlbumToFile(int fileId, int albumId) 
        {
            AlbumId = albumId;
            FileId = fileId;
        }
    }
}
