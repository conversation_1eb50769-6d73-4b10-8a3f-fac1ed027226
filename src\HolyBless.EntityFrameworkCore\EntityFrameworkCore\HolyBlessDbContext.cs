using Microsoft.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using System.Linq;
using System;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Entities.Channels;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Lookups;
using HolyBless.Entities.Books;
using HolyBless.StorageProviders;
using HolyBless.Entities.Albums;

namespace HolyBless.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ConnectionStringName("Default")]
public class HolyBlessDbContext :
    AbpDbContext<HolyBlessDbContext>,
    IIdentityDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */

    public DbSet<EBook> EBooks { get; set; }
    public DbSet<StorageBucket> StorageBuckets { get; set; }

    public DbSet<BucketFile> BucketFiles { get; set; }
    public DbSet<StorageProvider> StorageProviders { get; set; }
    public DbSet<StorageProviderToCountry> StorageProviderToCountries { get; set; }
    public DbSet<ProviderSecret> ProviderSecrets { get; set; }
    public DbSet<Article> Articles { get; set; }
    public DbSet<ArticleFile> ArticleFiles { get; set; }
    public DbSet<ArticleToTag> ArticleToTags { get; set; }
    public DbSet<TeacherArticleLink> TeacherArticleLinks { get; set; }
    public DbSet<Country> Countries { get; set; }
    public DbSet<Channel> Channels { get; set; }
    public DbSet<Collection> Collections { get; set; }
    public DbSet<CollectionToArticle> CollectionToArticles { get; set; }
    public DbSet<CollectionToFile> CollectionToFiles { get; set; }
    public DbSet<Tag> Tags { get; set; }
    public DbSet<FolderToBucketFile> FolderToBucketFiles { get; set; }
    public DbSet<VirtualDiskFolder> VirtualDiskFolders { get; set; }
    public DbSet<VirtualDiskFolderTree> VirtualDiskFolderTrees { get; set; }
    public DbSet<Album> Albums { get; set; }
    public DbSet<AlbumToFile> AlbumToFiles { get; set; }

    #region Entities from the modules

    /* Notice: We only implemented IIdentityProDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityProDbContext .
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    // Identity
    public DbSet<IdentityUser> Users { get; set; }

    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
    public DbSet<IdentitySession> Sessions { get; set; }

    #endregion Entities from the modules

    public HolyBlessDbContext(DbContextOptions<HolyBlessDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        /* Include modules to your migration db context */

        modelBuilder.ConfigurePermissionManagement();
        modelBuilder.ConfigureSettingManagement();
        modelBuilder.ConfigureBackgroundJobs();
        modelBuilder.ConfigureAuditLogging();
        modelBuilder.ConfigureFeatureManagement();
        modelBuilder.ConfigureIdentity();
        modelBuilder.ConfigureOpenIddict();
        modelBuilder.ConfigureBlobStoring();

        modelBuilder.ConfigureDomainModels();
    }
}