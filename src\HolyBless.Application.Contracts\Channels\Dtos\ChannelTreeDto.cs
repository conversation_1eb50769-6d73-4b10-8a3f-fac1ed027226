﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;

namespace HolyBless.Channels.Dtos
{
    /// <summary>
    /// Represents a tree structure for channels, used for hierarchical display in UI menu components.
    /// </summary>
    public class ChannelTreeDto
    {
        /// <summary>
        /// Channel Id
        /// </summary>
        public int Id { get; set; }

        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// When switching Language, use ContentCode to get corresponding channel Id for new language.
        /// </summary>
        public string? ContentCode { get; set; }

        /// <summary>
        /// Indicates if this is a root channel (main menu)
        /// </summary>
        public bool IsRoot { get; set; } = false;

        /// <summary>
        /// When a leave channel(menu item) is clicked,
        /// base on channel source, decide which component to render list page (Collection API, Alblum API, EBook API, Virtual Disk API etc.)
        /// </summary>
        public ChannelSource? ChannelSource { get; set; }

        /// <summary>
        /// Child channels of this channel, used to build menu hierarchy in UI.
        /// </summary>
        public List<ChannelTreeDto> Children { get; set; } = new List<ChannelTreeDto>();
    }
}