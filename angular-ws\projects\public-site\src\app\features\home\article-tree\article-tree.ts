import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderListModule } from 'primeng/orderlist';
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';

@Component({
  selector: 'app-artical-tree',
  standalone: true,
  imports: [CommonModule, TreeModule],
  templateUrl: './article-tree.html',
  styleUrls: ['./article-tree.scss'],
})
export class ArticleTreeComponent {
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #route = inject(ActivatedRoute);
  collectionId: number | null = null;
  constructor() {}
  files: TreeNode[] = [
    {
      label: 'Root',
      data: 'Root Node',
      expandedIcon: 'pi pi-folder-open',
      collapsedIcon: 'pi pi-folder',
    },
  ];

  selectedFile!: TreeNode;
  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.collectionId = params['collectionId'];
      this.loadCollectionSummary();
    });
  }

  loadCollectionSummary() {
    this.#ReadOnlyCollectionService
      .getCollectionTreeAndArticleTitles(this.collectionId!)
      .subscribe({
        next: (data) => {
          console.log('data',data)
          // this.files = data;
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
        },
      });
  }
}
