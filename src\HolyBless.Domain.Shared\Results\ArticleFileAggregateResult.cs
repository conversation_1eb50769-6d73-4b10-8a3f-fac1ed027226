using System;
using HolyBless.Enums;

namespace HolyBless.Results
{
    /// <summary>
    ///
    /// </summary>
    public class ArticleFileAggregateResult
    {
        // ArticleFile properties
        public int Id { get; set; }

        public int ArticleId { get; set; }
        public int FileId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public bool IsPrimary { get; set; }

        // BucketFile properties
        public string FileName { get; set; } = default!;

        public MediaType MediaType { get; set; }
        public ContentCategory ContentCategory { get; set; }
        public DateTime? FileDeliveryDate { get; set; }
        public int FileViews { get; set; }
        public string? YoutubeId { get; set; }

        public string FileUrl { get; set; } = default!; //Calculated URL of the file
    }
}