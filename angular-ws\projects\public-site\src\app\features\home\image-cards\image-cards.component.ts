import { Component, HostListener, inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleSummaryResult } from '@/proxy/holy-bless/results';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { I18nService } from '@/services/i18n.service';

@Component({
  selector: 'app-image-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    PaginatorModule,
    CalendarModule,
    ButtonModule,
    FormsModule,
  ],
  providers: [DatePipe],
  templateUrl: './image-cards.component.html',
  styleUrls: ['./image-cards.component.scss'],
})
export class ImageCardsComponent {
  i18nService = inject(I18nService);
  // 分页相关属性
  totalRecords = 50;
  rows = 10;
  first = 0;
  private _isMobile = false;

  // 日期选择器属性
  selectedDate: Date = new Date();
  collectionId: number | null = null;

  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #route = inject(ActivatedRoute);
  #datePipe = inject(DatePipe);
  router = inject(Router);

  // 模拟数据数组
  cardItems: ArticleSummaryResult[] = [];

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.collectionId = params['collectionId'];
      this.loadCollectionSummary();
    });
  }
  private loadCollectionSummary() {
    if (!this.collectionId) return;

    this.#ReadOnlyCollectionService
      .getCollectionSummary(this.collectionId, {
        skip: 0,
        maxResultCount: 10,
      })
      .subscribe({
        next: (data) => {
          this.cardItems = data.articles;
        },
        error: (error) => {
          console.error('获取摘要数据失败:', error);
        },
      });
  }

  navigateToArticle(articleId: number) {
    this.router.navigateByUrl(`/home/<USER>
  }

  formatDate(date: any): string {
    return (
      this.#datePipe.transform(date, 'yyyy-MM-dd HH:mm:ss', 'zh-Hans') || ''
    );
  }

  constructor() {
    this.checkMobile();
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10, 20, 50];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  // 分页事件处理
  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    console.log('页面变化:', event);
    // 这里可以添加数据加载逻辑
  }

  // 日期选择事件处理
  onDateChange(event: Date) {
    console.log('选择的日期:', event);
    // 这里可以添加按日期筛选数据的逻辑
  }

  // 自动播放功能
  isPlaying: boolean = false;
  playInterval: any;

  // 播放本页
  playCurrentPage() {
    if (this.isPlaying) {
      this.stopPlaying();
      return;
    }

    this.isPlaying = true;
    let currentIndex = 0;
    const items = this.cardItems;

    // 每3秒自动滚动到下一张
    this.playInterval = setInterval(() => {
      // 找到当前卡片元素并滚动到视图
      const cardElement = document.getElementById(
        `card-${items[currentIndex].id}`,
      );
      if (cardElement) {
        cardElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 添加高亮效果
        cardElement.classList.add('playing');
        setTimeout(() => {
          cardElement.classList.remove('playing');
        }, 2800); // 略小于间隔时间，让效果在下一次播放前消失
      }

      currentIndex++;
      if (currentIndex >= items.length) {
        // 播放完本页后停止
        this.stopPlaying();
      }
    }, 3000);
  }

  stopPlaying() {
    this.isPlaying = false;
    if (this.playInterval) {
      clearInterval(this.playInterval);
      this.playInterval = null;
    }
    // 移除所有可能的播放效果
    document.querySelectorAll('.playing').forEach((el) => {
      el.classList.remove('playing');
    });
  }
}
