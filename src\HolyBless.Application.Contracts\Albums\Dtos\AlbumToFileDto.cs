using Volo.Abp.Application.Dtos;

namespace HolyBless.Albums.Dtos
{
    public class AlbumToFileDto : EntityDto
    {
        public int AlbumId { get; set; }
        public int FileId { get; set; }
        public string? Title { get; set; }
        public int Weight { get; set; }
        
        // Additional properties for display
        public string? FileName { get; set; }
        public string? FileUrl { get; set; }
        public string? AlbumTitle { get; set; }
    }
}
