import type { EntityDto } from '@abp/ng.core';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentCategory } from '../../enums/content-category.enum';

export interface BucketFileDto extends EntityDto<number> {
  fileName?: string;
  title?: string;
  relativePathInBucket?: string;
  languageCode?: string;
  spokenLangCode?: string;
  mediaType?: MediaType;
  contentCategory?: ContentCategory;
  deliveryDate?: string;
  size?: number;
  views: number;
  youtubeId?: string;
  environment?: string;
  exists: boolean;
  computeUrl?: string;
}
