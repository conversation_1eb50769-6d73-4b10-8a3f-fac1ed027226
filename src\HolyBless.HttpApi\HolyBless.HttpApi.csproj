<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>HolyBless</RootNamespace>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\HolyBless.Application.Contracts\HolyBless.Application.Contracts.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.1.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.1.1" />
  </ItemGroup>
</Project>