﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class AlbumandFixManyToManyCascadeDelete : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ArticleToTags_Articles_ArticleId",
                table: "ArticleToTags");

            migrationBuilder.DropForeignKey(
                name: "FK_ArticleToTags_Tags_TagId",
                table: "ArticleToTags");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToArticles_Collections_CollectionId",
                table: "CollectionToArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToFiles_BucketFiles_FileId",
                table: "CollectionToFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToFiles_Collections_CollectionId",
                table: "CollectionToFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_TeacherArticleLinks_Articles_StudentArticleId",
                table: "TeacherArticleLinks");

            migrationBuilder.DropForeignKey(
                name: "FK_TeacherArticleLinks_Articles_TeacherArticleId",
                table: "TeacherArticleLinks");

            migrationBuilder.CreateTable(
                name: "Albums",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ChannelId = table.Column<int>(type: "integer", nullable: true),
                    ThumbnailFileId = table.Column<int>(type: "integer", nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Views = table.Column<int>(type: "integer", nullable: false),
                    Likes = table.Column<int>(type: "integer", nullable: false),
                    Weight = table.Column<int>(type: "integer", nullable: false),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    ContentCode = table.Column<string>(type: "text", nullable: true),
                    LanguageCode = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Albums", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Albums_BucketFiles_ThumbnailFileId",
                        column: x => x.ThumbnailFileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Albums_Channels_ChannelId",
                        column: x => x.ChannelId,
                        principalTable: "Channels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "AlbumToFiles",
                columns: table => new
                {
                    AlbumId = table.Column<int>(type: "integer", nullable: false),
                    FileId = table.Column<int>(type: "integer", nullable: false),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Weight = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AlbumToFiles", x => new { x.AlbumId, x.FileId });
                    table.ForeignKey(
                        name: "FK_AlbumToFiles_Albums_AlbumId",
                        column: x => x.AlbumId,
                        principalTable: "Albums",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AlbumToFiles_BucketFiles_FileId",
                        column: x => x.FileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Albums_ChannelId",
                table: "Albums",
                column: "ChannelId");

            migrationBuilder.CreateIndex(
                name: "IX_Albums_ThumbnailFileId",
                table: "Albums",
                column: "ThumbnailFileId");

            migrationBuilder.CreateIndex(
                name: "IX_AlbumToFiles_FileId",
                table: "AlbumToFiles",
                column: "FileId");

            migrationBuilder.AddForeignKey(
                name: "FK_ArticleToTags_Articles_ArticleId",
                table: "ArticleToTags",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ArticleToTags_Tags_TagId",
                table: "ArticleToTags",
                column: "TagId",
                principalTable: "Tags",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToArticles_Collections_CollectionId",
                table: "CollectionToArticles",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToFiles_BucketFiles_FileId",
                table: "CollectionToFiles",
                column: "FileId",
                principalTable: "BucketFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToFiles_Collections_CollectionId",
                table: "CollectionToFiles",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles",
                column: "BucketFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles",
                column: "FolderId",
                principalTable: "VirtualDiskFolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TeacherArticleLinks_Articles_StudentArticleId",
                table: "TeacherArticleLinks",
                column: "StudentArticleId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TeacherArticleLinks_Articles_TeacherArticleId",
                table: "TeacherArticleLinks",
                column: "TeacherArticleId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ArticleToTags_Articles_ArticleId",
                table: "ArticleToTags");

            migrationBuilder.DropForeignKey(
                name: "FK_ArticleToTags_Tags_TagId",
                table: "ArticleToTags");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToArticles_Collections_CollectionId",
                table: "CollectionToArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToFiles_BucketFiles_FileId",
                table: "CollectionToFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionToFiles_Collections_CollectionId",
                table: "CollectionToFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_TeacherArticleLinks_Articles_StudentArticleId",
                table: "TeacherArticleLinks");

            migrationBuilder.DropForeignKey(
                name: "FK_TeacherArticleLinks_Articles_TeacherArticleId",
                table: "TeacherArticleLinks");

            migrationBuilder.DropTable(
                name: "AlbumToFiles");

            migrationBuilder.DropTable(
                name: "Albums");

            migrationBuilder.AddForeignKey(
                name: "FK_ArticleToTags_Articles_ArticleId",
                table: "ArticleToTags",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ArticleToTags_Tags_TagId",
                table: "ArticleToTags",
                column: "TagId",
                principalTable: "Tags",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToArticles_Collections_CollectionId",
                table: "CollectionToArticles",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToFiles_BucketFiles_FileId",
                table: "CollectionToFiles",
                column: "FileId",
                principalTable: "BucketFiles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionToFiles_Collections_CollectionId",
                table: "CollectionToFiles",
                column: "CollectionId",
                principalTable: "Collections",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles",
                column: "BucketFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles",
                column: "FolderId",
                principalTable: "VirtualDiskFolders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TeacherArticleLinks_Articles_StudentArticleId",
                table: "TeacherArticleLinks",
                column: "StudentArticleId",
                principalTable: "Articles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TeacherArticleLinks_Articles_TeacherArticleId",
                table: "TeacherArticleLinks",
                column: "TeacherArticleId",
                principalTable: "Articles",
                principalColumn: "Id");
        }
    }
}
