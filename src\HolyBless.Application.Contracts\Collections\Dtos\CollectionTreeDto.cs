using System.Collections.Generic;
using HolyBless.Enums;

namespace HolyBless.Collections.Dtos
{
    public class CollectionTreeDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? ContentCode { get; set; }
        public int? ParentCollectionId { get; set; }
        public string? Description { get; set; }
        public PublishStatus Status { get; set; }
        public bool IsRoot { get; set; } = false; // Indicates if this is a root collection
        public List<CollectionTreeDto> Children { get; set; } = new List<CollectionTreeDto>();
    }
}
