using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.EntityFrameworkCore;
using HolyBless.Results;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using HolyBless.Domain.Interfaces;

namespace HolyBless.Repositories
{
    public class ArticleRepository : EfCoreRepository<HolyBlessDbContext, Article, int>, IArticleRepository
    {
        public ArticleRepository(IDbContextProvider<HolyBlessDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<ArticleAggregateResult?> GetArticleAggregateAsync(int articleId)
        {
            var results = await GetArticleAggregatesAsync(new List<int> { articleId });
            return results.FirstOrDefault();
        }

        public async Task<List<ArticleAggregateResult>> GetArticleAggregatesAsync(List<int> articleIds)
        {
            if (!articleIds.Any())
            {
                return new List<ArticleAggregateResult>();
            }

            var dbContext = await GetDbContextAsync();

            var articles = await dbContext.Articles
                .Where(a => articleIds.Contains(a.Id))
                .Include(a => a.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .Include(a => a.ThumbnailBucketFile)
                .OrderByDescending(a => a.DeliveryDate)
                .ToListAsync();

            var results = articles.Select(article => new ArticleAggregateResult
            {
                Id = article.Id,
                Title = article.Title,
                Description = article.Description,
                Keywords = article.Keywords,
                Views = article.Views,
                Likes = article.Likes,
                DeliveryDate = article.DeliveryDate,
                ArticleContentCategory = article.ArticleContentCategory,
                Status = article.Status,
                Content = article.Content,
                Memo = article.Memo,
                CreationTime = article.CreationTime,
                LastModificationTime = article.LastModificationTime,
                ThumbnailUrl = article.ThumbnailBucketFile?.ComputeUrl,
                ArticleFiles = article.ArticleFiles?.Select(af => new ArticleFileAggregateResult
                {
                    Id = af.Id,
                    FileId = af.FileId,
                    Title = af.Title,
                    Description = af.Description,
                    IsPrimary = af.IsPrimary,
                    FileName = af.BucketFile.FileName,
                    MediaType = af.BucketFile.MediaType,
                    ContentCategory = af.BucketFile.ContentCategory,
                    FileDeliveryDate = af.BucketFile.DeliveryDate,
                    FileViews = af.BucketFile.Views,
                    YoutubeId = af.BucketFile.YoutubeId,
                    FileUrl = af.BucketFile.ComputeUrl ?? ""
                }).ToList() ?? new List<ArticleFileAggregateResult>()
            }).ToList();

            return results;
        }
    }
}